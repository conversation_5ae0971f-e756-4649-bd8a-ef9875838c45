#!/bin/bash

# Bash script to install dependencies for WebSocket flow testing

echo "📦 Installing ATMA Testing Dependencies"
echo "======================================="

# Get the current directory (should be atma-backend)
current_dir=$(pwd)
echo "Current directory: $current_dir"

# Install root dependencies for testing
echo ""
echo "🔧 Installing root testing dependencies..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Root dependencies installed successfully"
else
    echo "❌ Failed to install root dependencies"
    exit 1
fi

# Function to install dependencies for a service
install_service_dependencies() {
    local service_name="$1"
    local service_path="$2"
    
    echo ""
    echo "🔧 Installing dependencies for $service_name..."
    
    # Check if directory exists
    if [ ! -d "$service_path" ]; then
        echo "❌ Directory not found: $service_path"
        return 1
    fi
    
    # Check if package.json exists
    if [ ! -f "$service_path/package.json" ]; then
        echo "⚠️ No package.json found in $service_path"
        return 0
    fi
    
    # Change to service directory and install
    cd "$service_path"
    npm install
    local success=$?
    cd "$current_dir"
    
    if [ $success -eq 0 ]; then
        echo "✅ $service_name dependencies installed"
        return 0
    else
        echo "❌ Failed to install $service_name dependencies"
        return 1
    fi
}

# Define services
declare -a services=(
    "Auth Service:$current_dir/auth-service"
    "Assessment Service:$current_dir/assessment-service"
    "Archive Service:$current_dir/archive-service"
    "Notification Service:$current_dir/notification-service"
    "API Gateway:$current_dir/api-gateway"
)

# Install dependencies for each service
success_count=0
total_services=${#services[@]}

for service_info in "${services[@]}"; do
    IFS=':' read -r name path <<< "$service_info"
    
    if install_service_dependencies "$name" "$path"; then
        ((success_count++))
    fi
done

echo ""
echo "📊 Installation Summary"
echo "======================="
echo "Services processed: $success_count/$total_services"

if [ $success_count -eq $total_services ]; then
    echo ""
    echo "✅ All dependencies installed successfully!"
    echo "You can now run the WebSocket flow test:"
    echo "1. Start all services: ./start-all-services.sh"
    echo "2. Run test: node test-websocket-flow.js"
else
    echo ""
    echo "⚠️ Some installations failed. Please check the error messages above."
fi

echo ""
echo "Press Enter to continue..."
read
