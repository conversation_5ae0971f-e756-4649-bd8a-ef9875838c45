const axios = require('axios');
const io = require('socket.io-client');

// Configuration
const API_GATEWAY_URL = 'http://localhost:3000';
const AUTH_SERVICE_URL = 'http://localhost:3001';
const NOTIFICATION_SERVICE_URL = 'http://localhost:3005';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

const profileData = {
  username: 'testuser',
  full_name: 'Test User',
  school_id: 1,
  date_of_birth: '1995-01-01',
  gender: 'male'
};

// Assessment data for WebSocket flow testing
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30,
    openness: 80
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 65,
    perseverance: 70,
    honesty: 75,
    zest: 60,
    love: 55,
    kindness: 68,
    socialIntelligence: 72,
    teamwork: 65,
    fairness: 70,
    leadership: 60,
    forgiveness: 55,
    humility: 50,
    prudence: 65,
    selfRegulation: 70,
    appreciationOfBeauty: 75,
    gratitude: 80,
    hope: 70,
    humor: 65,
    spirituality: 45
  }
};

let authToken = '';
let socket = null;
let jobId = '';
let userId = '';

// Helper function to make API calls
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message }
    };
  }
}

// Helper function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testHealthCheck() {
  console.log('\n=== 1. HEALTH CHECK ===');

  const services = [
    { name: 'API Gateway', url: `${API_GATEWAY_URL}/health` },
    { name: 'Auth Service', url: `${API_GATEWAY_URL}/api/auth/health` },
    { name: 'Assessment Service', url: `${API_GATEWAY_URL}/api/assessment/health` },
    { name: 'Archive Service', url: `${API_GATEWAY_URL}/api/archive/health` },
    { name: 'Notification Service', url: `${NOTIFICATION_SERVICE_URL}/health` }
  ];

  let allHealthy = true;

  for (const service of services) {
    console.log(`\nChecking ${service.name}...`);
    const result = await makeRequest('GET', service.url);
    const status = result.success ? '✅ HEALTHY' : '❌ UNHEALTHY';
    console.log(`${service.name}: ${status}`);

    if (!result.success) {
      allHealthy = false;
      console.log(`Error: ${JSON.stringify(result.data, null, 2)}`);
    }
  }

  return allHealthy;
}

async function testRegister() {
  console.log('\n=== 2. USER REGISTRATION ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, testUser);
  
  console.log('Registration Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('Auth token saved for subsequent requests');
  }
  
  return result.success;
}

async function testLogin() {
  console.log('\n=== 3. USER LOGIN ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, testUser);
  
  console.log('Login Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    userId = result.data.data.user.id;
    console.log('Auth token updated from login response');
    console.log(`User ID: ${userId}`);
  }

  return result.success;
}

async function testGetProfile() {
  console.log('\n=== 4. GET USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Get Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

async function testUpdateProfile() {
  console.log('\n=== 5. UPDATE USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Update Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

async function testDeleteProfile() {
  console.log('\n=== 6. DELETE USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Delete Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

// WebSocket Flow Testing Functions
async function testConnectWebSocket() {
  console.log('\n=== 7. WEBSOCKET CONNECTION ===');

  return new Promise((resolve, reject) => {
    console.log(`Connecting to WebSocket at ${NOTIFICATION_SERVICE_URL}...`);

    socket = io(NOTIFICATION_SERVICE_URL, {
      transports: ['websocket'],
      timeout: 10000
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      console.log(`Socket ID: ${socket.id}`);

      // Authenticate the socket
      console.log('Authenticating WebSocket...');
      socket.emit('authenticate', { token: authToken });
    });

    socket.on('authenticated', (data) => {
      console.log('✅ WebSocket authenticated');
      console.log('Auth data:', JSON.stringify(data, null, 2));
      resolve(true);
    });

    socket.on('auth_error', (error) => {
      console.log('❌ WebSocket authentication failed');
      console.log('Error:', JSON.stringify(error, null, 2));
      resolve(false);
    });

    socket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection failed');
      console.log('Error:', error.message);
      resolve(false);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
    });

    // Set up notification listeners
    socket.on('analysis-complete', (data) => {
      console.log('\n🎉 ANALYSIS COMPLETE NOTIFICATION RECEIVED!');
      console.log('Notification data:', JSON.stringify(data, null, 2));
    });

    socket.on('analysis-failed', (data) => {
      console.log('\n❌ ANALYSIS FAILED NOTIFICATION RECEIVED!');
      console.log('Notification data:', JSON.stringify(data, null, 2));
    });

    // Timeout after 10 seconds
    setTimeout(() => {
      if (!socket.connected) {
        console.log('❌ WebSocket connection timeout');
        resolve(false);
      }
    }, 10000);
  });
}

async function testSubmitAssessment() {
  console.log('\n=== 8. SUBMIT ASSESSMENT ===');

  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }

  console.log('Submitting assessment data...');
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/assessment/submit`, assessmentData, {
    'Authorization': `Bearer ${authToken}`
  });

  console.log('Submit Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.success && result.data.data?.jobId) {
    jobId = result.data.data.jobId;
    console.log(`Job ID saved: ${jobId}`);
    return true;
  }

  return false;
}

async function testMonitorJobStatus() {
  console.log('\n=== 9. MONITOR JOB STATUS ===');

  if (!jobId) {
    console.log('❌ No job ID available');
    return false;
  }

  console.log(`Monitoring job: ${jobId}`);
  console.log('Waiting for notifications...');

  let attempts = 0;
  const maxAttempts = 30; // 5 minutes with 10-second intervals

  while (attempts < maxAttempts) {
    attempts++;
    console.log(`\nAttempt ${attempts}/${maxAttempts} - Checking job status...`);

    const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
      'Authorization': `Bearer ${authToken}`
    });

    if (result.success) {
      const job = result.data.data;
      console.log(`Job Status: ${job.status}`);
      console.log(`Created: ${job.created_at}`);
      console.log(`Updated: ${job.updated_at}`);

      if (job.status === 'completed') {
        console.log('✅ Job completed successfully!');
        if (job.result_id) {
          console.log(`Result ID: ${job.result_id}`);
        }
        return true;
      } else if (job.status === 'failed') {
        console.log('❌ Job failed!');
        if (job.error_message) {
          console.log(`Error: ${job.error_message}`);
        }
        return false;
      }
    } else {
      console.log('❌ Failed to get job status');
      console.log('Response:', JSON.stringify(result.data, null, 2));
    }

    // Wait 10 seconds before next check
    console.log('Waiting 10 seconds before next check...');
    await wait(10000);
  }

  console.log('⏰ Timeout reached while waiting for job completion');
  return false;
}

async function testGetJobFromArchive() {
  console.log('\n=== 10. GET JOB FROM ARCHIVE ===');

  if (!jobId) {
    console.log('❌ No job ID available');
    return false;
  }

  console.log(`Getting job details for: ${jobId}`);
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
    'Authorization': `Bearer ${authToken}`
  });

  console.log('Get Job Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  return result.success;
}

function cleanup() {
  console.log('\n=== 🧹 CLEANUP ===');

  if (socket) {
    console.log('Disconnecting WebSocket...');
    socket.disconnect();
    socket = null;
  }

  console.log('Cleanup completed');
}

// WebSocket Flow Test Runner
async function runWebSocketFlow() {
  console.log('🚀 Starting ATMA WebSocket Flow Test');
  console.log('====================================');

  try {
    // Check all services health
    const servicesHealthy = await testHealthCheck();
    if (!servicesHealthy) {
      console.log('\n⚠️ Some services are not healthy. Please start all services before testing.');
      return;
    }

    // Authenticate user (try login first, then register if needed)
    const loginSuccess = await testLogin();
    if (!loginSuccess) {
      console.log('\n⚠️ Login failed, trying registration...');
      const registerSuccess = await testRegister();
      if (!registerSuccess) {
        console.log('\n❌ Authentication failed. Cannot proceed with WebSocket flow.');
        return;
      }
    }

    // Connect to WebSocket
    const wsConnected = await testConnectWebSocket();
    if (!wsConnected) {
      console.log('\n❌ WebSocket connection failed. Cannot proceed with flow.');
      return;
    }

    // Submit assessment
    const submitSuccess = await testSubmitAssessment();
    if (!submitSuccess) {
      cleanup();
      return;
    }

    // Monitor job status and wait for notifications
    const jobCompleted = await testMonitorJobStatus();

    // Get final job details from archive
    await testGetJobFromArchive();

    console.log('\n🎉 WebSocket Flow Test Completed!');
    console.log('==================================');

    if (jobCompleted) {
      console.log('✅ Test Result: SUCCESS - Job completed and notifications received');
    } else {
      console.log('⚠️ Test Result: PARTIAL - Job may still be processing');
    }

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  } finally {
    cleanup();
  }
}

// Main test runner (original auth flow)
async function runTests() {
  console.log('🚀 Starting ATMA User Flow Tests');
  console.log('=====================================');

  try {
    // Run all tests in sequence
    await testHealthCheck();

    const registerSuccess = await testRegister();
    if (!registerSuccess) {
      console.log('\n⚠️ Registration failed, trying login instead...');
      await testLogin();
    }

    await testGetProfile();
    await testUpdateProfile();

    // Get profile again to see the updated data
    console.log('\n=== 4b. GET UPDATED PROFILE ===');
    await testGetProfile();

    await testDeleteProfile();

    console.log('\n🎉 All tests completed!');
    console.log('=====================================');

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  // Check command line arguments
  const args = process.argv.slice(2);

  if (args.includes('--websocket') || args.includes('-w')) {
    console.log('Running WebSocket Flow Test...\n');
    runWebSocketFlow();
  } else if (args.includes('--help') || args.includes('-h')) {
    console.log('ATMA Testing Options:');
    console.log('  node test-user-flow.js           - Run basic auth flow tests');
    console.log('  node test-user-flow.js --websocket  - Run WebSocket flow test');
    console.log('  node test-user-flow.js -w           - Run WebSocket flow test (short)');
    console.log('  node test-user-flow.js --help       - Show this help');
  } else {
    console.log('Running basic auth flow tests...');
    console.log('Use --websocket or -w flag to run WebSocket flow test\n');
    runTests();
  }
}

module.exports = {
  runTests,
  runWebSocketFlow,
  testHealthCheck,
  testRegister,
  testLogin,
  testGetProfile,
  testUpdateProfile,
  testDeleteProfile,
  testConnectWebSocket,
  testSubmitAssessment,
  testMonitorJobStatus,
  testGetJobFromArchive,
  cleanup
};
