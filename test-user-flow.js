const axios = require('axios');

// Configuration
const API_GATEWAY_URL = 'http://localhost:3000';
const AUTH_SERVICE_URL = 'http://localhost:3001';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

const profileData = {
  username: 'testuser',
  full_name: 'Test User',
  school_id: 1,
  date_of_birth: '1995-01-01',
  gender: 'male'
};

let authToken = '';

// Helper function to make API calls
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message }
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('\n=== 1. HEALTH CHECK ===');
  
  // Test API Gateway health
  console.log('Testing API Gateway health...');
  const gatewayHealth = await makeRequest('GET', `${API_GATEWAY_URL}/health`);
  console.log('Gateway Health:', gatewayHealth.success ? '✅ OK' : '❌ FAILED');
  if (gatewayHealth.data) {
    console.log('Response:', JSON.stringify(gatewayHealth.data, null, 2));
  }
  
  // Test Auth Service health through gateway
  console.log('\nTesting Auth Service health through gateway...');
  const authHealth = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/health`);
  console.log('Auth Service Health:', authHealth.success ? '✅ OK' : '❌ FAILED');
  if (authHealth.data) {
    console.log('Response:', JSON.stringify(authHealth.data, null, 2));
  }
}

async function testRegister() {
  console.log('\n=== 2. USER REGISTRATION ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, testUser);
  
  console.log('Registration Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('Auth token saved for subsequent requests');
  }
  
  return result.success;
}

async function testLogin() {
  console.log('\n=== 3. USER LOGIN ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, testUser);
  
  console.log('Login Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('Auth token updated from login response');
  }
  
  return result.success;
}

async function testGetProfile() {
  console.log('\n=== 4. GET USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Get Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

async function testUpdateProfile() {
  console.log('\n=== 5. UPDATE USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Update Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

async function testDeleteProfile() {
  console.log('\n=== 6. DELETE USER PROFILE ===');
  
  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const result = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Delete Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  return result.success;
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting ATMA User Flow Tests');
  console.log('=====================================');
  
  try {
    // Run all tests in sequence
    await testHealthCheck();
    
    const registerSuccess = await testRegister();
    if (!registerSuccess) {
      console.log('\n⚠️ Registration failed, trying login instead...');
      await testLogin();
    }
    
    await testGetProfile();
    await testUpdateProfile();
    
    // Get profile again to see the updated data
    console.log('\n=== 4b. GET UPDATED PROFILE ===');
    await testGetProfile();
    
    await testDeleteProfile();
    
    console.log('\n🎉 All tests completed!');
    console.log('=====================================');
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testHealthCheck,
  testRegister,
  testLogin,
  testGetProfile,
  testUpdateProfile,
  testDeleteProfile
};
