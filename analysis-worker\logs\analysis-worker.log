{"level":"info","maxRecords":50,"message":"UsageTracker initialized","retentionDays":7,"service":"analysis-worker","timestamp":"2025-07-19 14:14:08","version":"1.0.0"}
{"component":"usage_tracker","health":{"criticalAlerts":0,"isHealthy":false,"warningAlerts":1},"level":"info","message":"Usage monitoring report generated","metrics":{"alertCount":1,"bufferUtilization":16,"estimatedCost":0.000149,"successRate":75,"totalRequests":8,"totalTokens":300},"reportType":"usage_monitoring","service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"component":"usage_tracker","level":"info","message":"Real-time usage metrics","metrics":{"current":{"last24Hours":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300},"lastHour":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300}},"performance":{"averageResponseTime":1428,"errorRate":25,"tokenCountingLatency":214},"system":{"bufferUtilization":16,"isHealthy":false,"memoryUsageKB":2,"status":"healthy"},"timestamp":"2025-07-19T07:14:09.038Z"},"service":"analysis-worker","structured":true,"timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"exportSize":2195,"format":"json","level":"info","message":"Usage data exported for monitoring","recordCount":8,"service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"Usage statistics reset successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"UsageTracker destroyed successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","maxRecords":10000,"message":"UsageTracker initialized","retentionDays":30,"service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0","workerConcurrency":"10"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 04:44:41","version":"1.0.0"}
{"level":"info","maxRecords":10000,"message":"UsageTracker initialized","retentionDays":30,"service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0","workerConcurrency":"10"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 05:14:54","userEmail":"<EMAIL>","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","userEmail":"<EMAIL>","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:14:54","url":"/jobs/6940778e-c41f-4951-a97b-1740b4a4b1ac/status","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","useMockModel":true,"version":"1.0.0"}
{"inputTokens":1165,"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Mock token counting completed","mock":true,"service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:14:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:15:24","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-20 05:15:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","url":"/results","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"errorMessage":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Saving failed analysis result to Archive Service","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","url":"/results","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"warn","message":"Failed to save failed analysis result to Archive Service","service":"analysis-worker","timestamp":"2025-07-20 05:15:24","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"failed","timestamp":"2025-07-20 05:15:24","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","url":"/jobs/6940778e-c41f-4951-a97b-1740b4a4b1ac/status","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:24","version":"1.0.0"}
{"error":"Request failed with status code 404","errorMessage":"Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to update assessment job status to failed","resultId":null,"service":"analysis-worker","timestamp":"2025-07-20 05:15:25","version":"1.0.0"}
{"jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"info","message":"Assessment job status updated to failed and tokens refunded","resultId":null,"service":"analysis-worker","timestamp":"2025-07-20 05:15:25","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 05:15:25","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","message":"Failed to process assessment job","processingTime":"30203ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 05:15:25","userId":"0c53685f-0821-4586-bcbc-c4151d7977c7","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 404","jobId":"6940778e-c41f-4951-a97b-1740b4a4b1ac","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 05:15:25","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 05:15:37","userEmail":"<EMAIL>","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-20 05:15:37","userEmail":"<EMAIL>","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 05:15:37","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:37","url":"/jobs/188459f4-670f-414c-b77a-740e41f08efd/status","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:15:37","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:15:37","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:15:37","useMockModel":true,"version":"1.0.0"}
{"inputTokens":1248,"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Mock token counting completed","mock":true,"service":"analysis-worker","timestamp":"2025-07-20 05:15:37","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 05:15:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:15:54","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-20 05:16:07","version":"1.0.0","weaknessesCount":3}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","url":"/results","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"errorMessage":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Saving failed analysis result to Archive Service","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","url":"/results","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"warn","message":"Failed to save failed analysis result to Archive Service","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"failed","timestamp":"2025-07-20 05:16:07","version":"1.0.0"}
{"error":"Request failed with status code 404","level":"error","message":"Archive service response error","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","url":"/jobs/188459f4-670f-414c-b77a-740e41f08efd/status","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":404,"statusText":"Not Found","timestamp":"2025-07-20 05:16:07","version":"1.0.0"}
{"errorMessage":"Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Assessment job status updated to failed","resultId":null,"service":"analysis-worker","timestamp":"2025-07-20 05:16:07","version":"1.0.0"}
{"jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"info","message":"Assessment job status updated to failed and tokens refunded","resultId":null,"service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","message":"Failed to process assessment job","processingTime":"30173ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 05:16:07","userId":"7f24ed4d-1d92-4173-a44d-0e35d3123243","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 404","jobId":"188459f4-670f-414c-b77a-740e41f08efd","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 05:16:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:16:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:16:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:17:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 05:17:54","version":"1.0.0"}
{"level":"info","maxRecords":10000,"message":"UsageTracker initialized","retentionDays":30,"service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0","workerConcurrency":"10"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-20 06:56:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:56:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:57:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:57:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:58:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:58:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:59:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 06:59:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:00:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:00:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:01:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:01:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:02:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:02:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:03:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:03:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:04:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:04:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:05:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:05:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:06:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:06:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:07:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:07:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 07:08:13","version":"1.0.0"}
