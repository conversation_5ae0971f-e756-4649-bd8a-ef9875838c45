# WebSocket Flow Test Documentation

## Overview
Script testing ini menguji flow lengkap yang melibatkan:
1. **WebSocket Connection** ke notification-service
2. **Submit Assessment** melalui assessment-service
3. **Monitor Job Status** dan menerima notifikasi real-time
4. **Get Job Details** dari archive-service

## Prerequisites

### 1. Install Dependencies
Pastikan semua dependencies sudah terinstall:

```bash
# Install socket.io-client untuk WebSocket testing
npm install socket.io-client

# Atau jika belum ada axios
npm install axios socket.io-client
```

### 2. Database Setup
Pastikan database PostgreSQL sudah running dan semua service sudah dikonfigurasi dengan benar.

### 3. Environment Variables
Pastikan semua service memiliki environment variables yang diperlukan (JWT_SECRET, database config, dll).

## How to Run

### Step 1: Start All Services

#### Windows (PowerShell):
```powershell
# Jalankan script PowerShell untuk start semua service
.\start-all-services.ps1
```

#### Linux/Mac (Bash):
```bash
# Beri permission execute pada script
chmod +x start-all-services.sh

# Jalankan script
./start-all-services.sh
```

#### Manual Start (Alternative):
Jika script otomatis tidak bekerja, start manual di terminal terpisah:

```bash
# Terminal 1 - Auth Service
cd auth-service
npm start

# Terminal 2 - Assessment Service  
cd assessment-service
npm start

# Terminal 3 - Archive Service
cd archive-service
npm start

# Terminal 4 - Notification Service
cd notification-service
npm start

# Terminal 5 - API Gateway
cd api-gateway
npm start
```

### Step 2: Wait for Services to Initialize
Tunggu sekitar 30 detik untuk memastikan semua service sudah fully initialized.

### Step 3: Run WebSocket Flow Test
```bash
node test-websocket-flow.js
```

## What the Test Does

### 1. Health Check
- Mengecek status semua service (API Gateway, Auth, Assessment, Archive, Notification)
- Memastikan semua service healthy sebelum melanjutkan testing

### 2. User Authentication
- Login dengan user test (<EMAIL>)
- Jika login gagal, akan mencoba register user baru
- Menyimpan JWT token untuk request selanjutnya

### 3. WebSocket Connection
- Connect ke notification-service di `http://localhost:3005`
- Authenticate WebSocket connection menggunakan JWT token
- Setup listener untuk notifikasi `analysis-complete` dan `analysis-failed`

### 4. Submit Assessment
- Submit assessment data lengkap (RIASEC, OCEAN, VIA-IS) ke assessment-service
- Mendapatkan jobId dari response
- Assessment akan masuk ke queue untuk diproses oleh analysis-worker

### 5. Monitor Job Status
- Polling job status setiap 10 detik ke archive-service
- Menunggu notifikasi real-time melalui WebSocket
- Timeout setelah 5 menit jika job belum selesai

### 6. Get Final Job Details
- Mengambil detail lengkap job dari archive-service
- Menampilkan result_id jika job berhasil completed

## Expected Output

### Successful Flow:
```
🚀 Starting ATMA WebSocket Flow Test
====================================

=== 🏥 CHECKING ALL SERVICES HEALTH ===
✅ All services are healthy!

=== 🔐 USER AUTHENTICATION ===
✅ Authentication successful
User ID: 12345678-1234-1234-1234-123456789012

=== 🔌 WEBSOCKET CONNECTION ===
✅ WebSocket connected
✅ WebSocket authenticated

=== 📝 SUBMIT ASSESSMENT ===
✅ Submit successful
Job ID saved: job_12345678-1234-1234-1234-123456789012

=== 👀 MONITORING JOB STATUS ===
Monitoring job: job_12345678-1234-1234-1234-123456789012

🎉 ANALYSIS COMPLETE NOTIFICATION RECEIVED!
✅ Job completed successfully!

=== 📁 GET JOB FROM ARCHIVE SERVICE ===
✅ Job details retrieved successfully

🎉 WebSocket Flow Test Completed!
✅ Test Result: SUCCESS - Job completed and notifications received
```

## Troubleshooting

### Service Not Healthy
```
⚠️ Some services are not healthy. Please start all services before testing.
```
**Solution**: Pastikan semua service sudah running dan accessible di port yang benar.

### WebSocket Connection Failed
```
❌ WebSocket connection failed
```
**Solution**: 
- Pastikan notification-service running di port 3005
- Check firewall settings
- Pastikan CORS configuration benar

### Authentication Failed
```
❌ Authentication failed
```
**Solution**:
- Pastikan auth-service running
- Check database connection
- Pastikan JWT_SECRET sudah dikonfigurasi

### Job Timeout
```
⏰ Timeout reached while waiting for job completion
```
**Solution**:
- Pastikan analysis-worker service running
- Check RabbitMQ connection
- Check database connection di archive-service

## Service Ports

| Service | Port | URL |
|---------|------|-----|
| API Gateway | 3000 | http://localhost:3000 |
| Auth Service | 3001 | http://localhost:3001 |
| Archive Service | 3002 | http://localhost:3002 |
| Assessment Service | 3003 | http://localhost:3003 |
| Notification Service | 3005 | http://localhost:3005 |

## Test Data

### User Credentials:
- Email: `<EMAIL>`
- Password: `password123`

### Assessment Data:
Script menggunakan data assessment lengkap dengan:
- **RIASEC**: 6 dimensi (Realistic, Investigative, Artistic, Social, Enterprising, Conventional)
- **OCEAN**: 5 dimensi (Conscientiousness, Extraversion, Agreeableness, Neuroticism, Openness)
- **VIA-IS**: 24 character strengths

## Notes

1. **Real-time Notifications**: Test ini menguji kemampuan sistem untuk mengirim notifikasi real-time melalui WebSocket ketika analysis selesai.

2. **End-to-End Flow**: Test mencakup seluruh flow dari submit assessment hingga mendapatkan hasil dari archive service.

3. **Error Handling**: Script memiliki error handling yang baik dan akan memberikan informasi yang jelas jika ada yang gagal.

4. **Cleanup**: Script akan otomatis disconnect WebSocket connection setelah test selesai.

5. **Monitoring**: Anda bisa monitor log di masing-masing service window untuk melihat proses yang terjadi di backend.
