# PowerShell script to start all ATMA services
# Run this script before executing the WebSocket flow test

Write-Host "🚀 Starting ATMA Services" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Function to start a service in a new PowerShell window
function Start-Service {
    param(
        [string]$ServiceName,
        [string]$ServicePath,
        [string]$StartCommand = "npm start"
    )
    
    Write-Host "Starting $ServiceName..." -ForegroundColor Yellow
    
    # Check if directory exists
    if (-not (Test-Path $ServicePath)) {
        Write-Host "❌ Directory not found: $ServicePath" -ForegroundColor Red
        return $false
    }
    
    # Start the service in a new PowerShell window
    $scriptBlock = "cd '$ServicePath'; $StartCommand; Read-Host 'Press Enter to close this window'"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $scriptBlock
    
    Write-Host "✅ $ServiceName started in new window" -ForegroundColor Green
    return $true
}

# Get the current directory (should be atma-backend)
$currentDir = Get-Location

Write-Host "Current directory: $currentDir" -ForegroundColor Cyan

# Define services to start
$services = @(
    @{
        Name = "Auth Service"
        Path = Join-Path $currentDir "auth-service"
        Command = "npm start"
    },
    @{
        Name = "Assessment Service"
        Path = Join-Path $currentDir "assessment-service"
        Command = "npm start"
    },
    @{
        Name = "Archive Service"
        Path = Join-Path $currentDir "archive-service"
        Command = "npm start"
    },
    @{
        Name = "Notification Service"
        Path = Join-Path $currentDir "notification-service"
        Command = "npm start"
    },
    @{
        Name = "API Gateway"
        Path = Join-Path $currentDir "api-gateway"
        Command = "npm start"
    }
)

# Start each service
$successCount = 0
foreach ($service in $services) {
    $success = Start-Service -ServiceName $service.Name -ServicePath $service.Path -StartCommand $service.Command
    if ($success) {
        $successCount++
    }
    
    # Wait a bit between starting services
    Start-Sleep -Seconds 2
}

Write-Host "`n📊 Service Startup Summary" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host "Services started: $successCount/$($services.Count)" -ForegroundColor $(if ($successCount -eq $services.Count) { "Green" } else { "Yellow" })

if ($successCount -eq $services.Count) {
    Write-Host "`n✅ All services started successfully!" -ForegroundColor Green
    Write-Host "Wait about 30 seconds for all services to fully initialize," -ForegroundColor Yellow
    Write-Host "then run the WebSocket flow test:" -ForegroundColor Yellow
    Write-Host "node test-websocket-flow.js" -ForegroundColor Cyan
} else {
    Write-Host "`n⚠️ Some services failed to start. Please check the error messages above." -ForegroundColor Yellow
}

Write-Host "`n📝 Notes:" -ForegroundColor Cyan
Write-Host "- Each service will open in a separate PowerShell window" -ForegroundColor White
Write-Host "- Check each window for any startup errors" -ForegroundColor White
Write-Host "- Services should be accessible on these ports:" -ForegroundColor White
Write-Host "  • API Gateway: http://localhost:3000" -ForegroundColor White
Write-Host "  • Auth Service: http://localhost:3001" -ForegroundColor White
Write-Host "  • Archive Service: http://localhost:3002" -ForegroundColor White
Write-Host "  • Assessment Service: http://localhost:3003" -ForegroundColor White
Write-Host "  • Notification Service: http://localhost:3005" -ForegroundColor White

Write-Host "`nPress Enter to continue..." -ForegroundColor Gray
Read-Host
