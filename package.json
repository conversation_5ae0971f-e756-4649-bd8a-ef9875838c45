{"name": "atma-backend", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:auth": "node test-user-flow.js", "test:websocket": "node test-websocket-flow.js", "start:services": "powershell -ExecutionPolicy Bypass -File start-all-services.ps1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "socket.io-client": "^4.7.5"}}