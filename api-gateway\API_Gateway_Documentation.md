# ATMA API Gateway Documentation

## Overview
API Gateway untuk sistem ATMA yang menyediakan unified endpoint untuk semua microservices dengan fitur authentication, rate limiting, dan monitoring.

## Base URL
```
http://localhost:3000
```

## Service Mapping

### Auth Service Endpoints
**Base Service URL**: `http://localhost:3001`

| Gateway Endpoint | Service Endpoint | Method | Auth Required | Rate Limit |
|------------------|------------------|--------|---------------|------------|
| `/api/auth/register` | `/auth/register` | POST | ❌ | Auth Limiter |
| `/api/auth/register/batch` | `/auth/register/batch` | POST | ❌ | Auth Limiter |
| `/api/auth/login` | `/auth/login` | POST | ❌ | Auth Limiter |
| `/api/auth/logout` | `/auth/logout` | POST | ✅ | General |
| `/api/auth/change-password` | `/auth/change-password` | POST | ✅ | General |
| `/api/auth/profile` | `/auth/profile` | GET/PUT/DELETE | ✅ | General |
| `/api/auth/token-balance` | `/auth/token-balance` | GET | ✅ | General |
| `/api/auth/schools` | `/auth/schools` | GET/POST | ✅ | General |
| `/api/auth/schools/by-location` | `/auth/schools/by-location` | GET | ✅ | General |
| `/api/auth/schools/location-stats` | `/auth/schools/location-stats` | GET | ✅ | General |
| `/api/auth/schools/:schoolId/users` | `/auth/schools/:schoolId/users` | GET | ✅ | General |
| `/api/auth/schools/distribution` | `/auth/schools/distribution` | GET | ✅ | General |
| `/api/auth/verify-token` | `/auth/verify-token` | POST | 🔧 Internal | None |
| `/api/auth/token-balance` | `/auth/token-balance` | PUT | 🔧 Internal | None |

### Admin Endpoints
| Gateway Endpoint | Service Endpoint | Method | Auth Required | Rate Limit |
|------------------|------------------|--------|---------------|------------|
| `/api/admin/login` | `/admin/login` | POST | ❌ | Auth Limiter |
| `/api/admin/profile` | `/admin/profile` | GET/PUT | ✅ Admin | Admin Limiter |

### Archive Service Endpoints
**Base Service URL**: `http://localhost:3002`

| Gateway Endpoint | Service Endpoint | Method | Auth Required | Rate Limit |
|------------------|------------------|--------|---------------|------------|
| `/api/archive/results` | `/archive/results` | GET/POST | ✅ | General |
| `/api/archive/results/:id` | `/archive/results/:id` | GET/PUT/DELETE | ✅ | General |
| `/api/archive/results/batch` | `/archive/results/batch` | POST | 🔧 Internal | None |
| `/api/archive/jobs` | `/archive/jobs` | GET/POST | ✅ | General |
| `/api/archive/jobs/:jobId` | `/archive/jobs/:jobId` | GET/PUT/DELETE | ✅ | General |
| `/api/archive/jobs/stats` | `/archive/jobs/stats` | GET | ✅ | General |
| `/api/archive/batch/stats` | `/archive/batch/stats` | GET | 🔧 Internal | None |
| `/api/archive/batch/process` | `/archive/batch/process` | POST | 🔧 Internal | None |
| `/api/archive/batch/clear` | `/archive/batch/clear` | POST | 🔧 Internal | None |

### Assessment Service Endpoints
**Base Service URL**: `http://localhost:3003`

| Gateway Endpoint | Service Endpoint | Method | Auth Required | Rate Limit |
|------------------|------------------|--------|---------------|------------|
| `/api/assessment/submit` | `/assessments/submit` | POST | ✅ | Assessment Limiter |
| `/api/assessment/status/:jobId` | `/assessments/status/:jobId` | GET | ✅ | General |
| `/api/assessment/callback/completed` | `/assessments/callback/completed` | POST | 🔧 Internal | None |
| `/api/assessment/callback/failed` | `/assessments/callback/failed` | POST | 🔧 Internal | None |

## Authentication Types

### 🔑 User Authentication
**Header**: `Authorization: Bearer <jwt_token>`

**Example**:
```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     http://localhost:3000/api/auth/profile
```

### 🔧 Internal Service Authentication
**Headers**: 
- `X-Service-Key: <internal_service_key>`
- `X-Internal-Service: true`

**Example**:
```bash
curl -H "X-Service-Key: your_internal_service_key" \
     -H "X-Internal-Service: true" \
     http://localhost:3000/api/auth/verify-token
```

### 👑 Admin Authentication
**Header**: `Authorization: Bearer <admin_jwt_token>`
**Requirement**: User must have `user_type: "admin"`

## Rate Limiting

### General Limiter
- **Window**: 15 minutes
- **Max Requests**: 100 per IP
- **Applies to**: Most endpoints
- **Key**: IP address + User ID (if authenticated)

### Auth Limiter
- **Window**: 15 minutes
- **Max Requests**: 10 per IP
- **Applies to**: Login, register endpoints
- **Key**: IP address

### Assessment Limiter
- **Window**: 1 hour
- **Max Requests**: 5 per user
- **Applies to**: Assessment submission
- **Key**: `assessment-{user_id}` or `assessment-{ip}`

### Admin Limiter
- **Window**: 15 minutes
- **Max Requests**: 200 per IP
- **Applies to**: Admin endpoints
- **Key**: IP address

## Health Check Endpoints

### Basic Health Check
```http
GET /health
```

**Response**:
```json
{
  "success": true,
  "status": "healthy",
  "service": "api-gateway",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "development"
}
```

### Detailed Health Check
```http
GET /health/detailed
```

**Response**:
```json
{
  "success": true,
  "status": "healthy",
  "service": "api-gateway",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "development",
  "services": {
    "auth": {
      "status": "healthy",
      "url": "http://localhost:3001",
      "responseTime": "50ms",
      "lastChecked": "2024-01-01T00:00:00.000Z"
    },
    "archive": {
      "status": "healthy",
      "url": "http://localhost:3002",
      "responseTime": "45ms",
      "lastChecked": "2024-01-01T00:00:00.000Z"
    },
    "assessment": {
      "status": "healthy",
      "url": "http://localhost:3003",
      "responseTime": "60ms",
      "lastChecked": "2024-01-01T00:00:00.000Z"
    }
  },
  "summary": {
    "total": 3,
    "healthy": 3,
    "unhealthy": 0
  }
}
```

### Readiness Probe
```http
GET /health/ready
```

### Liveness Probe
```http
GET /health/live
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common Error Codes
- `UNAUTHORIZED` (401): Missing or invalid authentication
- `FORBIDDEN` (403): Insufficient permissions
- `VALIDATION_ERROR` (400): Invalid input data
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `SERVICE_UNAVAILABLE` (503): Backend service down
- `GATEWAY_TIMEOUT` (504): Service request timeout
- `NOT_FOUND` (404): Route or resource not found
- `INTERNAL_ERROR` (500): Internal server error

## Request/Response Examples

### User Registration
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Assessment Submission
```bash
curl -X POST http://localhost:3000/api/assessment/submit \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30,
    "openness": 80
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  }
}'
```

### Get Analysis Results
```bash
curl -H "Authorization: Bearer <token>" \
     "http://localhost:3000/api/archive/results?page=1&limit=10"
```

## Security Features

### CORS Configuration
- Configurable allowed origins
- Credentials support
- Preflight handling

### Security Headers (Helmet.js)
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy

### Request Validation
- JSON body size limits (10MB)
- URL encoding limits
- Header validation

## Monitoring & Logging

### Request Logging
- Morgan middleware untuk HTTP request logging
- Configurable log formats
- IP address tracking

### Error Logging
- Detailed error logging dengan stack traces
- Request context dalam error logs
- Timestamp dan user information

### Proxy Logging
- Request/response logging untuk service calls
- Response time tracking
- Service availability monitoring
