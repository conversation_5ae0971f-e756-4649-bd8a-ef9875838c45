# PowerShell script to install dependencies for WebSocket flow testing

Write-Host "📦 Installing ATMA Testing Dependencies" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Get the current directory (should be atma-backend)
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Cyan

# Install root dependencies for testing
Write-Host "`n🔧 Installing root testing dependencies..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Root dependencies installed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to install root dependencies" -ForegroundColor Red
    exit 1
}

# Function to install dependencies for a service
function Install-ServiceDependencies {
    param(
        [string]$ServiceName,
        [string]$ServicePath
    )
    
    Write-Host "`n🔧 Installing dependencies for $ServiceName..." -ForegroundColor Yellow
    
    # Check if directory exists
    if (-not (Test-Path $ServicePath)) {
        Write-Host "❌ Directory not found: $ServicePath" -ForegroundColor Red
        return $false
    }
    
    # Check if package.json exists
    $packageJsonPath = Join-Path $ServicePath "package.json"
    if (-not (Test-Path $packageJsonPath)) {
        Write-Host "⚠️ No package.json found in $ServicePath" -ForegroundColor Yellow
        return $true
    }
    
    # Change to service directory and install
    Push-Location $ServicePath
    npm install
    $success = $LASTEXITCODE -eq 0
    Pop-Location
    
    if ($success) {
        Write-Host "✅ $ServiceName dependencies installed" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to install $ServiceName dependencies" -ForegroundColor Red
    }
    
    return $success
}

# Define services
$services = @(
    @{
        Name = "Auth Service"
        Path = Join-Path $currentDir "auth-service"
    },
    @{
        Name = "Assessment Service"
        Path = Join-Path $currentDir "assessment-service"
    },
    @{
        Name = "Archive Service"
        Path = Join-Path $currentDir "archive-service"
    },
    @{
        Name = "Notification Service"
        Path = Join-Path $currentDir "notification-service"
    },
    @{
        Name = "API Gateway"
        Path = Join-Path $currentDir "api-gateway"
    }
)

# Install dependencies for each service
$successCount = 0
foreach ($service in $services) {
    $success = Install-ServiceDependencies -ServiceName $service.Name -ServicePath $service.Path
    if ($success) {
        $successCount++
    }
}

Write-Host "`n📊 Installation Summary" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan
Write-Host "Services processed: $successCount/$($services.Count)" -ForegroundColor $(if ($successCount -eq $services.Count) { "Green" } else { "Yellow" })

if ($successCount -eq $services.Count) {
    Write-Host "`n✅ All dependencies installed successfully!" -ForegroundColor Green
    Write-Host "You can now run the WebSocket flow test:" -ForegroundColor Yellow
    Write-Host "1. Start all services: .\start-all-services.ps1" -ForegroundColor Cyan
    Write-Host "2. Run test: node test-websocket-flow.js" -ForegroundColor Cyan
} else {
    Write-Host "`n⚠️ Some installations failed. Please check the error messages above." -ForegroundColor Yellow
}

Write-Host "`nPress Enter to continue..." -ForegroundColor Gray
Read-Host
