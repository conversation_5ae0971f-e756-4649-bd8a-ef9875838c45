#!/bin/bash

# Bash script to start all ATMA services
# Run this script before executing the WebSocket flow test

echo "🚀 Starting ATMA Services"
echo "========================="

# Function to start a service in a new terminal
start_service() {
    local service_name="$1"
    local service_path="$2"
    local start_command="${3:-npm start}"
    
    echo "Starting $service_name..."
    
    # Check if directory exists
    if [ ! -d "$service_path" ]; then
        echo "❌ Directory not found: $service_path"
        return 1
    fi
    
    # Detect terminal and start service
    if command -v gnome-terminal &> /dev/null; then
        # Linux with GNOME
        gnome-terminal --title="$service_name" --working-directory="$service_path" -- bash -c "$start_command; echo 'Press Enter to close...'; read"
    elif command -v xterm &> /dev/null; then
        # Linux with xterm
        xterm -title "$service_name" -e "cd '$service_path' && $start_command && echo 'Press Enter to close...' && read" &
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        osascript -e "tell application \"Terminal\" to do script \"cd '$service_path' && $start_command\""
    else
        # Fallback - run in background
        echo "⚠️ No suitable terminal found. Starting $service_name in background..."
        cd "$service_path"
        $start_command &
        cd - > /dev/null
    fi
    
    echo "✅ $service_name started"
    return 0
}

# Get the current directory (should be atma-backend)
current_dir=$(pwd)
echo "Current directory: $current_dir"

# Define services to start
declare -a services=(
    "Auth Service:$current_dir/auth-service:npm start"
    "Assessment Service:$current_dir/assessment-service:npm start"
    "Archive Service:$current_dir/archive-service:npm start"
    "Notification Service:$current_dir/notification-service:npm start"
    "API Gateway:$current_dir/api-gateway:npm start"
)

# Start each service
success_count=0
total_services=${#services[@]}

for service_info in "${services[@]}"; do
    IFS=':' read -r name path command <<< "$service_info"
    
    if start_service "$name" "$path" "$command"; then
        ((success_count++))
    fi
    
    # Wait a bit between starting services
    sleep 2
done

echo ""
echo "📊 Service Startup Summary"
echo "========================="
echo "Services started: $success_count/$total_services"

if [ $success_count -eq $total_services ]; then
    echo ""
    echo "✅ All services started successfully!"
    echo "Wait about 30 seconds for all services to fully initialize,"
    echo "then run the WebSocket flow test:"
    echo "node test-websocket-flow.js"
else
    echo ""
    echo "⚠️ Some services failed to start. Please check the error messages above."
fi

echo ""
echo "📝 Notes:"
echo "- Each service should open in a separate terminal window"
echo "- Check each window for any startup errors"
echo "- Services should be accessible on these ports:"
echo "  • API Gateway: http://localhost:3000"
echo "  • Auth Service: http://localhost:3001"
echo "  • Archive Service: http://localhost:3002"
echo "  • Assessment Service: http://localhost:3003"
echo "  • Notification Service: http://localhost:3005"

echo ""
echo "Press Enter to continue..."
read
